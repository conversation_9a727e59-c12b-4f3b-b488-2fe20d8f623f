import { z } from "zod";

// Constants cho validation
const CATEGORY_NAME_MIN_LENGTH = 1;
const CATEGORY_NAME_MAX_LENGTH = 50;
const CATEGORY_DESCRIPTION_MAX_LENGTH = 500; // Tăng từ 200 lên 500
const CATEGORY_ORDER_MIN = 1;
const CATEGORY_ORDER_MAX = 9999;

// Predefined colors cho category (export để dùng ở components)
export const VALID_CATEGORY_COLORS = [
  "#3B82F6", "#10B981", "#EF4444", "#F59E0B", "#8B5CF6",
  "#06B6D4", "#EC4899", "#84CC16", "#F97316", "#6B7280",
  "#1F2937", "#7C3AED", "#DC2626", "#059669", "#0EA5E9"
];

// Helper schemas
export const categoryIdSchema = z
  .string()
  .min(1, "ID category không hợp lệ")
  .uuid("ID category phải là UUID hợp lệ")
  .or(z.string().regex(/^[a-zA-Z0-9_-]+$/, "ID category chỉ được chứa chữ cái, số, gạch ngang và gạch dưới"));

const categoryNameSchema = z
  .string()
  .min(CATEGORY_NAME_MIN_LENGTH, "Tên category không được để trống")
  .max(CATEGORY_NAME_MAX_LENGTH, `Tên category không được quá ${CATEGORY_NAME_MAX_LENGTH} ký tự`)
  .regex(
    /^[a-zA-Z0-9\s\u00C0-\u024F\u1E00-\u1EFF\-_\.]+$/,
    "Tên category chỉ được chứa chữ cái, số, khoảng trắng và các ký tự đặc biệt: - _ ."
  )
  .transform((val) => val.trim()) // Tự động trim whitespace
  .refine((val) => val.length > 0, "Tên category không được chỉ chứa khoảng trắng");

const categoryDescriptionSchema = z
  .string()
  .max(CATEGORY_DESCRIPTION_MAX_LENGTH, `Mô tả không được quá ${CATEGORY_DESCRIPTION_MAX_LENGTH} ký tự`)
  .transform((val) => val?.trim() || "")
  .optional();

const categoryColorSchema = z
  .string()
  .regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, "Màu sắc phải là mã hex hợp lệ (ví dụ: #FF0000)")
  .refine(
    (color) => {
      // Kiểm tra xem có phải màu hợp lệ không (không quá sáng hoặc quá tối)
      const hex = color.replace('#', '');
      const r = parseInt(hex.substring(0, 2), 16);
      const g = parseInt(hex.substring(2, 4), 16);
      const b = parseInt(hex.substring(4, 6), 16);
      const brightness = (r * 299 + g * 587 + b * 114) / 1000;
      return brightness >= 30 && brightness <= 225; // Không quá tối hoặc quá sáng
    },
    "Màu sắc không phù hợp cho hiển thị text"
  )
  .default("#6B7280");

const categoryOrderSchema = z
  .number()
  .int("Thứ tự phải là số nguyên")
  .min(CATEGORY_ORDER_MIN, `Thứ tự phải từ ${CATEGORY_ORDER_MIN} trở lên`)
  .max(CATEGORY_ORDER_MAX, `Thứ tự không được vượt quá ${CATEGORY_ORDER_MAX}`);

// Schema cho việc tạo mới category
export const createCategorySchema = z.object({
  name: categoryNameSchema,
  description: categoryDescriptionSchema,
  color: categoryColorSchema,
  isActive: z.boolean().default(true),
  projectId: z.string().uuid("Project ID phải là UUID hợp lệ").optional(),
  tags: z.array(z.string().min(1).max(20)).max(5, "Không được có quá 5 tags").optional(),
});

// Schema cho việc cập nhật category
export const updateCategorySchema = createCategorySchema.partial().extend({
  id: categoryIdSchema,
});

// Schema cho việc sắp xếp lại category
export const reorderCategorySchema = z.object({
  categories: z
    .array(
      z.object({
        id: categoryIdSchema,
        order: categoryOrderSchema,
      })
    )
    .min(1, "Phải có ít nhất 1 category để sắp xếp")
    .max(100, "Không thể sắp xếp quá 100 categories cùng lúc"),
});

// Schema cho việc xóa category
export const deleteCategorySchema = z.object({
  id: categoryIdSchema,
  force: z.boolean().default(false).optional(), // Cho phép force delete
});

// Schema cho bulk operations
export const bulkDeleteCategorySchema = z.object({
  ids: z
    .array(categoryIdSchema)
    .min(1, "Phải chọn ít nhất 1 category để xóa")
    .max(50, "Không thể xóa quá 50 categories cùng lúc"),
  force: z.boolean().default(false).optional(),
});

export const bulkUpdateCategorySchema = z.object({
  ids: z
    .array(categoryIdSchema)
    .min(1, "Phải chọn ít nhất 1 category để cập nhật")
    .max(50, "Không thể cập nhật quá 50 categories cùng lúc"),
  updates: z.object({
    isActive: z.boolean().optional(),
    color: categoryColorSchema.optional(),
    projectId: z.string().uuid().optional(),
  }),
});

// Schema cho search và filter
export const categoryFilterSchema = z.object({
  search: z.string().max(100).optional(),
  isActive: z.boolean().optional(),
  projectId: z.string().uuid().optional(),
  tags: z.array(z.string()).optional(),
  colors: z.array(categoryColorSchema).optional(),
});

export const categorySortSchema = z.object({
  field: z.enum(["name", "createdAt", "updatedAt", "order", "issueCount"]),
  direction: z.enum(["asc", "desc"]),
});

export const categoryQuerySchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(10),
  filter: categoryFilterSchema.optional(),
  sort: categorySortSchema.optional(),
});

// Schema cho import/export
export const importCategorySchema = z.object({
  categories: z.array(
    createCategorySchema.extend({
      externalId: z.string().optional(), // ID từ hệ thống khác
    })
  ),
  options: z.object({
    skipDuplicates: z.boolean().default(true),
    updateExisting: z.boolean().default(false),
  }),
});

// Export types
export type CreateCategoryFormValues = z.infer<typeof createCategorySchema>;
export type UpdateCategoryFormValues = z.infer<typeof updateCategorySchema>;
export type ReorderCategoryValues = z.infer<typeof reorderCategorySchema>;
export type DeleteCategoryValues = z.infer<typeof deleteCategorySchema>;
export type BulkDeleteCategoryValues = z.infer<typeof bulkDeleteCategorySchema>;
export type BulkUpdateCategoryValues = z.infer<typeof bulkUpdateCategorySchema>;
export type CategoryFilterValues = z.infer<typeof categoryFilterSchema>;
export type CategorySortValues = z.infer<typeof categorySortSchema>;
export type CategoryQueryValues = z.infer<typeof categoryQuerySchema>;
export type ImportCategoryValues = z.infer<typeof importCategorySchema>;

// Validation helpers
export const validateCategoryName = (name: string) => {
  return categoryNameSchema.safeParse(name);
};

export const validateCategoryColor = (color: string) => {
  return categoryColorSchema.safeParse(color);
};

export const validateCategoryId = (id: string) => {
  return categoryIdSchema.safeParse(id);
};

// Backward compatibility
export const categorySchema = createCategorySchema;
export type CategoryFormValues = CreateCategoryFormValues;
